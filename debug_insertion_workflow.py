#!/usr/bin/env python3
"""
Debug crítico: Analisar workflow de inserção de dados na ROM
"""

import sys
import os
import struct

# Adicionar o diretório scripts ao path
sys.path.append('scripts')

def debug_insertion_workflow():
    """Analisa o workflow completo de inserção"""
    
    print("🚨 DEBUG CRÍTICO: Workflow de Inserção de Dados")
    print("=" * 60)
    
    try:
        from insert import (
            LoadProjectPokemonDatabase,
            FindTrainerTable,
            ReadAllTrainerDataFromOriginalROM
        )
        
        # Carregar dados do projeto
        project_pokemon_data = LoadProjectPokemonDatabase()
        
        print(f"🔍 INVESTIGAÇÃO 1: Estrutura de Dados do Pokémon")
        print("-" * 50)
        
        # Verificar estrutura de dados de um Pokémon na ROM
        with open("test.gba", "rb") as rom:
            trainer_table_offset = FindTrainerTable(rom)
            koga_id = 418
            
            # Ler dados do trainer Koga
            trainer_offset = trainer_table_offset + (koga_id * 40)
            rom.seek(trainer_offset)
            trainer_data = rom.read(40)
            
            party_size = trainer_data[32]
            party_ptr = struct.unpack('<I', trainer_data[36:40])[0]
            party_offset = party_ptr - 0x08000000
            
            print(f"   Koga - Party size: {party_size}, offset: 0x{party_offset:08X}")
            
            # Ler party e analisar estrutura byte por byte
            rom.seek(party_offset)
            party_data = rom.read(party_size * 8)
            
            print(f"\n   Análise byte por byte dos Pokémon:")
            for i in range(party_size):
                pokemon_offset = i * 8
                pokemon_data = party_data[pokemon_offset:pokemon_offset + 8]
                
                if len(pokemon_data) >= 8:
                    print(f"\n   Slot {i+1} (offset {pokemon_offset}):")
                    print(f"      Raw bytes: {' '.join(f'{b:02X}' for b in pokemon_data)}")
                    
                    # Analisar cada campo
                    iv = pokemon_data[0]
                    level = pokemon_data[2]
                    species_id = struct.unpack('<H', pokemon_data[4:6])[0]
                    
                    print(f"      IV: {iv}")
                    print(f"      Level: {level} {'← IMPOSSÍVEL!' if level > 100 else ''}")
                    print(f"      Species: {species_id}")
                    
                    # Verificar se species existe no projeto
                    if species_id in project_pokemon_data:
                        pokemon_data_proj = project_pokemon_data[species_id]
                        name = pokemon_data_proj.get('name', 'Unknown')
                        print(f"      Nome: {name}")
                    else:
                        print(f"      Nome: NÃO ENCONTRADO no projeto")
                    
                    # Tentar interpretações alternativas
                    print(f"      Interpretações alternativas:")
                    
                    # Talvez o level esteja em outro byte?
                    for byte_pos in [1, 3]:
                        alt_level = pokemon_data[byte_pos]
                        if 1 <= alt_level <= 100:
                            print(f"         Byte {byte_pos}: Level {alt_level} ← POSSÍVEL")
                    
                    # Talvez species esteja em outro offset?
                    for offset in [0, 2, 6]:
                        if offset + 2 <= len(pokemon_data):
                            alt_species = struct.unpack('<H', pokemon_data[offset:offset+2])[0]
                            if 1 <= alt_species <= 1440:
                                if alt_species in project_pokemon_data:
                                    alt_name = project_pokemon_data[alt_species].get('name', 'Unknown')
                                    print(f"         Offset {offset}: Species {alt_species} ({alt_name}) ← POSSÍVEL")
        
        print(f"\n🔍 INVESTIGAÇÃO 2: Comparar com ROM Original")
        print("-" * 50)
        
        # Comparar com ROM original
        if os.path.exists("BPRE0.gba"):
            with open("BPRE0.gba", "rb") as rom_orig:
                trainer_table_offset = FindTrainerTable(rom_orig)
                
                # Ler dados do trainer Koga na ROM original
                trainer_offset = trainer_table_offset + (koga_id * 40)
                rom_orig.seek(trainer_offset)
                trainer_data = rom_orig.read(40)
                
                party_size_orig = trainer_data[32]
                party_ptr_orig = struct.unpack('<I', trainer_data[36:40])[0]
                party_offset_orig = party_ptr_orig - 0x08000000
                
                print(f"   ROM Original - Party size: {party_size_orig}, offset: 0x{party_offset_orig:08X}")
                
                # Ler party original
                rom_orig.seek(party_offset_orig)
                party_data_orig = rom_orig.read(party_size_orig * 8)
                
                print(f"\n   Comparação ROM Original vs Compilada:")
                for i in range(min(party_size_orig, party_size)):
                    pokemon_offset = i * 8
                    
                    # ROM Original
                    pokemon_data_orig = party_data_orig[pokemon_offset:pokemon_offset + 8]
                    level_orig = pokemon_data_orig[2]
                    species_orig = struct.unpack('<H', pokemon_data_orig[4:6])[0]
                    
                    # ROM Compilada
                    pokemon_data_comp = party_data[pokemon_offset:pokemon_offset + 8]
                    level_comp = pokemon_data_comp[2]
                    species_comp = struct.unpack('<H', pokemon_data_comp[4:6])[0]
                    
                    print(f"   Slot {i+1}:")
                    print(f"      Original:  Species #{species_orig:3d} Level {level_orig:3d}")
                    print(f"      Compilada: Species #{species_comp:3d} Level {level_comp:3d}")
                    
                    if level_comp > 100:
                        print(f"      🚨 PROBLEMA: Level impossível na ROM compilada!")
                    
                    if species_orig != species_comp:
                        print(f"      🔄 MODIFICADO: Species alterado")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro durante debug: {e}")
        import traceback
        traceback.print_exc()
        return False

def debug_insertion_functions():
    """Debug das funções de inserção específicas"""
    
    print(f"\n🔍 INVESTIGAÇÃO 3: Funções de Inserção")
    print("-" * 50)
    
    try:
        # Verificar as funções que fazem inserção na ROM
        with open("scripts/insert.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # Procurar por padrões de escrita na ROM
        import re
        
        write_patterns = [
            r'rom\.write\(',
            r'struct\.pack_into\(',
            r'pokemon_data\[2\]',
            r'pokemon_data\[4:6\]'
        ]
        
        print(f"   Procurando padrões de escrita na ROM:")
        
        lines = content.split('\n')
        for i, line in enumerate(lines):
            for pattern in write_patterns:
                if re.search(pattern, line):
                    print(f"      Linha {i+1}: {line.strip()}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro durante debug de funções: {e}")
        return False

def debug_specific_insertion():
    """Debug da inserção específica do Koga"""
    
    print(f"\n🔍 INVESTIGAÇÃO 4: Inserção Específica do Koga")
    print("-" * 50)
    
    try:
        from insert import LoadProjectPokemonDatabase
        
        project_pokemon_data = LoadProjectPokemonDatabase()
        
        # IDs que deveriam ter sido inseridos segundo os logs
        expected_insertions = {
            5: 531,  # FROSLASS
            6: 643   # FOONGUS
        }
        
        print(f"   IDs esperados para inserção:")
        for slot, species_id in expected_insertions.items():
            if species_id in project_pokemon_data:
                pokemon_data = project_pokemon_data[species_id]
                name = pokemon_data.get('name', 'Unknown')
                type1 = pokemon_data.get('type1')
                type2 = pokemon_data.get('type2')
                print(f"      Slot {slot}: Species #{species_id} ({name}) Type1={type1}, Type2={type2}")
            else:
                print(f"      Slot {slot}: Species #{species_id} NÃO ENCONTRADO")
        
        # IDs que realmente aparecem no jogo
        actual_game_results = {
            1: 109,  # KOFFING
            2: 109,  # KOFFING (deveria ser GASTLY #92)
            3: 89,   # MUK
            4: 109,  # KOFFING (deveria ser GASTLY #92)
            5: 531,  # FROSLASS ← CORRETO!
            6: 58    # GROWLITHE (deveria ser FOONGUS #643)
        }
        
        print(f"\n   IDs reais no jogo:")
        for slot, species_id in actual_game_results.items():
            if species_id in project_pokemon_data:
                pokemon_data = project_pokemon_data[species_id]
                name = pokemon_data.get('name', 'Unknown')
                type1 = pokemon_data.get('type1')
                type2 = pokemon_data.get('type2')
                print(f"      Slot {slot}: Species #{species_id} ({name}) Type1={type1}, Type2={type2}")
            else:
                print(f"      Slot {slot}: Species #{species_id} NÃO ENCONTRADO")
        
        print(f"\n   Análise de discrepâncias:")
        print(f"      ✅ Slot 5: Esperado #531 FROSLASS, obtido #531 FROSLASS ← CORRETO")
        print(f"      ❌ Slot 6: Esperado #643 FOONGUS, obtido #58 GROWLITHE ← INCORRETO")
        print(f"      ❌ Slots 2,4: Esperado #92 GASTLY, obtido #109 KOFFING ← INCORRETO")
        
        print(f"\n   Hipóteses:")
        print(f"      1. Offset de escrita incorreto")
        print(f"      2. Estrutura de dados incorreta")
        print(f"      3. Conversão de endianness incorreta")
        print(f"      4. Sobreposição de dados")
        print(f"      5. Cache/buffer não sincronizado")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro durante debug específico: {e}")
        return False

if __name__ == "__main__":
    # Mudar para o diretório do projeto
    os.chdir("e:/Desktop/StarLyraFireRed/Dynamic-Pokemon-Expansion-Gen-9-master")
    
    # Executar investigações
    debug_insertion_workflow()
    debug_insertion_functions()
    debug_specific_insertion()

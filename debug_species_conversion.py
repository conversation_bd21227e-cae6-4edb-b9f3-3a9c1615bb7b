#!/usr/bin/env python3
"""
Debug crítico: Investigar conversão entre Species ID e Pokédex Nacional
"""

import sys
import os
import struct

# Adicionar o diretório scripts ao path
sys.path.append('scripts')

def debug_species_conversion():
    """Investiga a conversão de Species ID"""
    
    print("🔍 DEBUG CRÍTICO: Conversão Species ID vs Pokédex Nacional")
    print("=" * 70)
    
    try:
        from insert import (
            ReadAllTrainerDataFromOriginalROM,
            LoadProjectPokemonDatabase,
            FindTrainerTable
        )
        
        # Carregar dados do projeto
        project_pokemon_data = LoadProjectPokemonDatabase()
        
        print(f"📋 VERIFICAÇÃO 1: Dados do Projeto")
        print(f"   Total Pokémon no projeto: {len(project_pokemon_data)}")
        
        # Verificar alguns Pokémon específicos
        test_species = [77, 92, 109, 643]  # <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Reshiram
        
        for species_id in test_species:
            if species_id in project_pokemon_data:
                pokemon_data = project_pokemon_data[species_id]
                name = pokemon_data.get('name', f'Species_{species_id}')
                print(f"   Species #{species_id:3d}: {name}")
            else:
                print(f"   Species #{species_id:3d}: NÃO ENCONTRADO no projeto")
        
        print(f"\n📋 VERIFICAÇÃO 2: Leitura da ROM Original")
        
        # Ler dados originais do Koga
        original_trainer_cache = ReadAllTrainerDataFromOriginalROM()
        if not original_trainer_cache:
            print("❌ Falha ao ler dados originais")
            return
        
        cached_trainers = original_trainer_cache['trainers']
        koga_id = 418
        
        if koga_id not in cached_trainers:
            print(f"❌ Koga não encontrado")
            return
        
        koga_info = cached_trainers[koga_id]
        original_party = koga_info['original_party']
        
        print(f"   Koga - Party original detectada pelo sistema:")
        for i, pokemon in enumerate(original_party):
            species = pokemon.get('species', 'Unknown')
            level = pokemon.get('level', 'Unknown')
            print(f"      Slot {i+1}: Species #{species} Level {level}")
        
        print(f"\n📋 VERIFICAÇÃO 3: Leitura Direta da ROM")
        
        # Ler diretamente da ROM original
        with open("BPRE0.gba", "rb") as rom:
            trainer_table_offset = FindTrainerTable(rom)
            
            # Ler dados do trainer Koga
            trainer_offset = trainer_table_offset + (koga_id * 40)
            rom.seek(trainer_offset)
            trainer_data = rom.read(40)
            
            party_size = trainer_data[32]
            party_ptr = struct.unpack('<I', trainer_data[36:40])[0]
            party_offset = party_ptr - 0x08000000
            
            print(f"   Koga - Leitura direta da ROM:")
            print(f"      Party size: {party_size}")
            print(f"      Party offset: 0x{party_offset:08X}")
            
            # Ler party diretamente
            rom.seek(party_offset)
            party_data = rom.read(party_size * 8)
            
            for i in range(party_size):
                pokemon_offset = i * 8
                pokemon_data = party_data[pokemon_offset:pokemon_offset + 8]
                
                if len(pokemon_data) < 8:
                    continue
                
                iv = pokemon_data[0]
                level = pokemon_data[2]
                species_id = struct.unpack('<H', pokemon_data[4:6])[0]
                
                print(f"      Slot {i+1}: Species #{species_id} Level {level} IV {iv}")
                
                # Verificar se é o Species ID correto
                if species_id == 77:
                    print(f"         🚨 Species #77 encontrado na ROM original!")
                elif species_id == 92:
                    print(f"         🚨 Species #92 encontrado na ROM original!")
                elif species_id == 109:
                    print(f"         ✅ Species #109 (Koffing) - correto")
                elif species_id == 110:
                    print(f"         ✅ Species #110 (Weezing) - correto")
                elif species_id == 89:
                    print(f"         ✅ Species #89 (Muk) - correto")
        
        print(f"\n📋 VERIFICAÇÃO 4: Conversão Species ID → Pokédex")
        
        # Verificar se há tabela de conversão no projeto
        print(f"   Procurando tabelas de conversão...")
        
        # Verificar se SPECIES_RESHIRAM existe
        print(f"\n📋 VERIFICAÇÃO 5: SPECIES_RESHIRAM")
        print(f"   SPECIES_RESHIRAM = 0x2B8 = {0x2B8} (Species ID)")
        print(f"   Reshiram Pokédex Nacional = #643")
        
        if 0x2B8 in project_pokemon_data:
            reshiram_data = project_pokemon_data[0x2B8]
            name = reshiram_data.get('name', 'Unknown')
            print(f"   Species #{0x2B8}: {name} (encontrado no projeto)")
        else:
            print(f"   Species #{0x2B8}: NÃO encontrado no projeto")
        
        if 643 in project_pokemon_data:
            species_643_data = project_pokemon_data[643]
            name = species_643_data.get('name', 'Unknown')
            print(f"   Species #643: {name} (encontrado no projeto)")
        else:
            print(f"   Species #643: NÃO encontrado no projeto")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro durante debug: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_species_mapping():
    """Verifica mapeamento de Species no projeto"""
    
    print(f"\n🔍 VERIFICAÇÃO: Mapeamento de Species no Projeto")
    print("=" * 60)
    
    try:
        # Verificar se há arquivos de mapeamento
        mapping_files = [
            "include/constants/species.h",
            "src/data/pokemon/species_info.h", 
            "include/constants/pokedex.h"
        ]
        
        for file_path in mapping_files:
            if os.path.exists(file_path):
                print(f"✅ Arquivo encontrado: {file_path}")
                
                # Procurar por SPECIES_RESHIRAM
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                
                if 'SPECIES_RESHIRAM' in content:
                    print(f"   🎯 SPECIES_RESHIRAM encontrado!")
                    
                    # Extrair definição
                    lines = content.split('\n')
                    for line in lines:
                        if 'SPECIES_RESHIRAM' in line:
                            print(f"      {line.strip()}")
                
                if 'SPECIES_PONYTA' in content:
                    print(f"   🐴 SPECIES_PONYTA encontrado!")
                    
                    # Extrair definição
                    lines = content.split('\n')
                    for line in lines:
                        if 'SPECIES_PONYTA' in line:
                            print(f"      {line.strip()}")
            else:
                print(f"❌ Arquivo não encontrado: {file_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro durante verificação: {e}")
        return False

def analyze_workflow_conversion():
    """Analisa onde a conversão falha no workflow"""
    
    print(f"\n🔍 ANÁLISE: Onde a Conversão Falha no Workflow")
    print("=" * 60)
    
    print(f"🎯 HIPÓTESES PRINCIPAIS:")
    print(f"   1. 🚨 Sistema lê Species ID da ROM mas trata como Pokédex Nacional")
    print(f"   2. 🚨 Função de conversão Species→Pokédex não existe ou está quebrada")
    print(f"   3. 🚨 Projeto usa Species ID mas logs mostram Pokédex Nacional")
    print(f"   4. 🚨 Múltiplas passadas de randomização sobrescrevem dados")
    
    print(f"\n💡 EVIDÊNCIAS:")
    print(f"   📋 ROM Original: Koffing, Muk, Koffing, Weezing")
    print(f"   📋 Sistema detecta: #109, #92, #89, #92")
    print(f"   📋 Jogo mostra: Koffing, Gastly, Muk, Gastly")
    print(f"   📋 Adicionados reais: Ponyta, Growlithe, Venusaur")
    print(f"   📋 Sistema reporta: #3, #643")
    
    print(f"\n🔧 SOLUÇÕES NECESSÁRIAS:")
    print(f"   1. Implementar conversão Species ID ↔ Pokédex Nacional")
    print(f"   2. Verificar se sistema usa Species ID consistentemente")
    print(f"   3. Corrigir logs para mostrar Species ID real")
    print(f"   4. Evitar múltiplas randomizações do mesmo trainer")
    
    return True

if __name__ == "__main__":
    # Mudar para o diretório do projeto
    os.chdir("e:/Desktop/StarLyraFireRed/Dynamic-Pokemon-Expansion-Gen-9-master")
    
    # Executar debug
    debug_species_conversion()
    check_species_mapping()
    analyze_workflow_conversion()

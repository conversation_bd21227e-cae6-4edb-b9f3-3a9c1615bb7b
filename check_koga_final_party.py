#!/usr/bin/env python3
"""
Verificação final: Party atual do Koga na ROM compilada
"""

import sys
import os
import struct

# Adicionar o diretório scripts ao path
sys.path.append('scripts')

def check_koga_final_party():
    """Verifica a party final de Koga na ROM compilada"""
    
    print("🎯 PARTY FINAL DO KOGA NA ROM COMPILADA")
    print("=" * 50)
    
    try:
        from insert import FindTrainerTable, LoadProjectPokemonDatabase
        
        # Carregar dados do projeto
        project_pokemon_data = LoadProjectPokemonDatabase()
        
        # Mapeamento de tipos
        type_names = {
            0: "NORMAL", 1: "FIGHTING", 2: "FLYING", 3: "POISON", 4: "GROUND",
            5: "ROCK", 6: "BUG", 7: "GHOST", 8: "STEEL", 9: "FIRE",
            10: "WATER", 11: "GRASS", 12: "ELECTRIC", 13: "<PERSON>YCH<PERSON>", 14: "ICE",
            15: "DRAGON", 16: "DARK", 17: "FAIRY", 18: "ROOSTLESS", 19: "BLANK"
        }
        
        # Verificar Koga na ROM compilada
        koga_id = 418
        
        print(f"🔍 VERIFICAÇÃO DE KOGA (ID {koga_id}) NA ROM COMPILADA:")
        
        with open("test.gba", "rb") as rom:
            trainer_table_offset = FindTrainerTable(rom)
            
            # Ler dados do trainer Koga
            trainer_offset = trainer_table_offset + (koga_id * 40)
            rom.seek(trainer_offset)
            trainer_data = rom.read(40)
            
            party_size = trainer_data[32]
            party_ptr = struct.unpack('<I', trainer_data[36:40])[0]
            party_offset = party_ptr - 0x08000000
            
            print(f"   Party size: {party_size}")
            print(f"   Party offset: 0x{party_offset:08X}")
            
            # Ler party atual
            rom.seek(party_offset)
            party_data = rom.read(party_size * 8)
            
            print(f"\n📋 PARTY ATUAL DE KOGA:")
            print(f"{'Slot':<4} {'Species':<8} {'Name':<15} {'Level':<5} {'Type':<20} {'Category':<10}")
            print("-" * 70)
            
            original_slots = 4  # Slots originais (1-4)
            additional_slots = party_size - original_slots  # Slots adicionais (5+)
            
            for i in range(party_size):
                pokemon_offset = i * 8
                pokemon_data = party_data[pokemon_offset:pokemon_offset + 8]
                
                if len(pokemon_data) < 8:
                    continue
                
                level = pokemon_data[2]
                species_id = struct.unpack('<H', pokemon_data[4:6])[0]
                
                # Determinar categoria do slot
                if i < original_slots:
                    category = "ORIGINAL"
                else:
                    category = "ADICIONAL"
                
                # Verificar dados do projeto
                if species_id in project_pokemon_data:
                    pokemon_data_proj = project_pokemon_data[species_id]
                    name = pokemon_data_proj.get('name', f'Species_{species_id}')
                    type1 = pokemon_data_proj.get('type1')
                    type2 = pokemon_data_proj.get('type2')
                    
                    type1_name = type_names.get(type1, f'Type_{type1}') if type1 is not None else 'None'
                    type2_name = type_names.get(type2, f'Type_{type2}') if type2 is not None and type2 != type1 else None
                    
                    type_str = f"{type1_name}" + (f"/{type2_name}" if type2_name else "")
                    
                    print(f"{i+1:<4} #{species_id:<7} {name:<15} {level:<5} {type_str:<20} {category:<10}")
                    
                    # Verificar adequação para Koga
                    if type1 == 3 or type2 == 3:  # POISON = 3
                        print(f"     ✅ POISON type - adequado para Koga")
                    elif type1 == 7 or type2 == 7:  # GHOST = 7
                        print(f"     ✅ GHOST type - adequado para Koga")
                    elif type1 == 9 or type2 == 9:  # FIRE = 9
                        print(f"     🚨 FIRE type - NÃO adequado para Koga!")
                    else:
                        print(f"     ⚠️ Outros tipos - verificar adequação")
                else:
                    print(f"{i+1:<4} #{species_id:<7} {'Unknown':<15} {level:<5} {'Unknown':<20} {category:<10}")
                    print(f"     ❌ Não encontrado no projeto")
            
            print(f"\n📊 ANÁLISE FINAL:")
            
            # Contar tipos
            poison_count = 0
            ghost_count = 0
            fire_count = 0
            other_count = 0
            
            for i in range(party_size):
                pokemon_offset = i * 8
                pokemon_data = party_data[pokemon_offset:pokemon_offset + 8]
                
                if len(pokemon_data) >= 8:
                    species_id = struct.unpack('<H', pokemon_data[4:6])[0]
                    
                    if species_id in project_pokemon_data:
                        pokemon_data_proj = project_pokemon_data[species_id]
                        type1 = pokemon_data_proj.get('type1')
                        type2 = pokemon_data_proj.get('type2')
                        
                        if type1 == 3 or type2 == 3:  # POISON
                            poison_count += 1
                        elif type1 == 7 or type2 == 7:  # GHOST
                            ghost_count += 1
                        elif type1 == 9 or type2 == 9:  # FIRE
                            fire_count += 1
                        else:
                            other_count += 1
            
            print(f"   🐍 Pokémon POISON: {poison_count}/{party_size}")
            print(f"   👻 Pokémon GHOST: {ghost_count}/{party_size}")
            print(f"   🔥 Pokémon FIRE: {fire_count}/{party_size}")
            print(f"   ❓ Outros tipos: {other_count}/{party_size}")
            
            # Verificar adequação geral
            adequate_count = poison_count + ghost_count
            print(f"\n🎯 ADEQUAÇÃO PARA KOGA:")
            print(f"   Pokémon adequados (POISON/GHOST): {adequate_count}/{party_size} ({adequate_count/party_size*100:.1f}%)")
            
            if fire_count > 0:
                print(f"   🚨 PROBLEMA: {fire_count} Pokémon FIRE encontrados!")
                print(f"   🔍 Isso indica que o sistema ainda não está funcionando perfeitamente")
            else:
                print(f"   ✅ SUCESSO: Nenhum Pokémon FIRE encontrado!")
                print(f"   🎯 Sistema funcionando corretamente")
            
            if adequate_count == party_size:
                print(f"   🏆 PERFEITO: Todos os Pokémon são adequados para Koga!")
            elif adequate_count >= party_size * 0.8:
                print(f"   ✅ BOM: Maioria dos Pokémon são adequados para Koga")
            else:
                print(f"   ⚠️ PARCIAL: Apenas {adequate_count} de {party_size} Pokémon são adequados")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro durante verificação: {e}")
        import traceback
        traceback.print_exc()
        return False

def compare_with_logs():
    """Compara com os logs de compilação"""
    
    print(f"\n📊 COMPARAÇÃO COM LOGS DE COMPILAÇÃO:")
    print("=" * 50)
    
    print(f"🎯 LOGS DE COMPILAÇÃO MOSTRARAM:")
    print(f"   🔍 DEBUG KOGA: Selecionando do PROJETO - Primary: 3, Secondary: 7, Party types: {{3, 7}}")
    print(f"   🔍 DEBUG KOGA: Selecionado #23 (Type1=3, Type2=3, Score=30)")
    print(f"   🔍 DEBUG KOGA: Selecionado #24 (Type1=3, Type2=3, Score=30)")
    print(f"   🔍 DEBUG KOGA: Selecionados 2 Pokémon: [23, 24]")
    print(f"   🔍 DEBUG KOGA: Inserindo #23 no slot 5")
    print(f"   🔍 DEBUG KOGA: Inserindo #24 no slot 6")
    print(f"   🏆 KOGA: Added #531, #643")
    
    print(f"\n💡 INTERPRETAÇÃO:")
    print(f"   - Sistema selecionou #23 e #24 (Ekans e Arbok)")
    print(f"   - Mas log final mostra #531 e #643")
    print(f"   - Isso indica múltiplas passadas ou substituições")
    print(f"   - Verificação da ROM mostrará o resultado final real")
    
    return True

if __name__ == "__main__":
    # Mudar para o diretório do projeto
    os.chdir("e:/Desktop/StarLyraFireRed/Dynamic-Pokemon-Expansion-Gen-9-master")
    
    # Executar verificação
    success = check_koga_final_party()
    compare_with_logs()
    
    if success:
        print(f"\n🎉 Verificação concluída!")
    else:
        print(f"\n⚠️ Erro durante verificação")

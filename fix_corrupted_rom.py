#!/usr/bin/env python3
"""
Correção crítica: ROM base corrompida com dados incorretos de Koga
"""

import sys
import os
import struct
import shutil

# Adicionar o diretório scripts ao path
sys.path.append('scripts')

def verify_rom_corruption():
    """Verifica se a ROM base está corrompida"""
    
    print("🔍 VERIFICAÇÃO: ROM Base Corrompida")
    print("=" * 50)
    
    try:
        from insert import FindTrainerTable
        
        # Verificar Koga na ROM original
        koga_id = 418
        
        print(f"🎯 VERIFICANDO KOGA (ID {koga_id}) NA ROM ORIGINAL:")
        
        with open("BPRE0.gba", "rb") as rom:
            trainer_table_offset = FindTrainerTable(rom)
            
            # Ler dados do trainer Koga
            trainer_offset = trainer_table_offset + (koga_id * 40)
            rom.seek(trainer_offset)
            trainer_data = rom.read(40)
            
            party_size = trainer_data[32]
            party_ptr = struct.unpack('<I', trainer_data[36:40])[0]
            party_offset = party_ptr - 0x08000000
            
            print(f"   Party size: {party_size}")
            print(f"   Party offset: 0x{party_offset:08X}")
            
            # Ler party atual
            rom.seek(party_offset)
            party_data = rom.read(party_size * 8)
            
            print(f"\n📋 PARTY ATUAL NA ROM:")
            corruption_found = False
            
            expected_party = [
                (109, "Koffing"),   # Slot 1
                (110, "Weezing"),   # Slot 2 - DEVERIA SER WEEZING!
                (89, "Muk"),        # Slot 3
                (109, "Koffing")    # Slot 4 - DEVERIA SER KOFFING!
            ]
            
            for i in range(party_size):
                pokemon_offset = i * 8
                pokemon_data = party_data[pokemon_offset:pokemon_offset + 8]
                
                if len(pokemon_data) < 8:
                    continue
                
                level = pokemon_data[2]
                species_id = struct.unpack('<H', pokemon_data[4:6])[0]
                
                expected_species, expected_name = expected_party[i]
                
                if species_id == expected_species:
                    print(f"   Slot {i+1}: #{species_id:3d} Level {level:2d} ✅ {expected_name} (CORRETO)")
                else:
                    print(f"   Slot {i+1}: #{species_id:3d} Level {level:2d} 🚨 CORROMPIDO!")
                    print(f"      Esperado: #{expected_species} {expected_name}")
                    print(f"      Encontrado: #{species_id} ({'Gastly' if species_id == 92 else 'Unknown'})")
                    corruption_found = True
            
            print(f"\n📊 RESULTADO:")
            if corruption_found:
                print(f"   🚨 ROM CORROMPIDA: Dados incorretos encontrados!")
                print(f"   🔧 SOLUÇÃO: Substituir por ROM Fire Red limpa")
                return False
            else:
                print(f"   ✅ ROM LIMPA: Todos os dados corretos")
                return True
        
    except Exception as e:
        print(f"❌ Erro durante verificação: {e}")
        import traceback
        traceback.print_exc()
        return False

def suggest_rom_sources():
    """Sugere fontes para ROM limpa"""
    
    print(f"\n💡 FONTES PARA ROM FIRE RED LIMPA:")
    print("=" * 40)
    
    print(f"🎯 ARQUIVO NECESSÁRIO:")
    print(f"   Nome: Pokemon Fire Red (U) [!].gba")
    print(f"   Tamanho: 16,777,216 bytes (16 MB)")
    print(f"   CRC32: 84EE4776")
    print(f"   MD5: E26EE0D44E809351C8CE2D73C7400CDD")
    print(f"   SHA1: 41CB23D8DCCC8EBD7C649CD8FBB58EEACE6E2FDC")
    
    print(f"\n🔧 PASSOS PARA CORREÇÃO:")
    print(f"   1. Obter ROM Fire Red limpa e original")
    print(f"   2. Renomear para 'BPRE0.gba'")
    print(f"   3. Substituir arquivo atual")
    print(f"   4. Executar 'python scripts/make.py' para recompilar")
    print(f"   5. Testar Koga novamente")
    
    print(f"\n⚠️ IMPORTANTE:")
    print(f"   - Use apenas ROM original e limpa")
    print(f"   - Não use ROM já modificada ou hackeada")
    print(f"   - Verifique checksums para garantir integridade")

def backup_current_rom():
    """Faz backup da ROM atual corrompida"""
    
    print(f"\n💾 BACKUP DA ROM CORROMPIDA:")
    print("=" * 35)
    
    try:
        if os.path.exists("BPRE0.gba"):
            backup_name = "BPRE0_corrupted_backup.gba"
            shutil.copy2("BPRE0.gba", backup_name)
            print(f"   ✅ Backup criado: {backup_name}")
            print(f"   📁 Tamanho: {os.path.getsize(backup_name):,} bytes")
            return True
        else:
            print(f"   ❌ ROM original não encontrada")
            return False
    
    except Exception as e:
        print(f"   ❌ Erro ao criar backup: {e}")
        return False

def check_rom_integrity():
    """Verifica integridade da ROM atual"""
    
    print(f"\n🔍 VERIFICAÇÃO DE INTEGRIDADE:")
    print("=" * 35)
    
    try:
        if not os.path.exists("BPRE0.gba"):
            print(f"   ❌ ROM não encontrada")
            return False
        
        file_size = os.path.getsize("BPRE0.gba")
        print(f"   📁 Tamanho: {file_size:,} bytes")
        
        if file_size == 16777216:  # 16 MB
            print(f"   ✅ Tamanho correto (16 MB)")
        else:
            print(f"   ⚠️ Tamanho incorreto (esperado: 16,777,216 bytes)")
        
        # Verificar header
        with open("BPRE0.gba", "rb") as rom:
            header = rom.read(0xA0)
            
            # Game title (0x0A0-0x0AB)
            rom.seek(0xA0)
            title = rom.read(12).decode('ascii', errors='ignore').strip('\x00')
            print(f"   🎮 Título: '{title}'")
            
            # Game code (0x0AC-0x0AF)
            rom.seek(0xAC)
            game_code = rom.read(4).decode('ascii', errors='ignore')
            print(f"   🔖 Código: '{game_code}'")
            
            if game_code == "BPRE":
                print(f"   ✅ Código correto (Fire Red USA)")
            else:
                print(f"   ⚠️ Código incorreto (esperado: BPRE)")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Erro durante verificação: {e}")
        return False

def main():
    """Função principal"""
    
    print("🚨 CORREÇÃO CRÍTICA: ROM Base Corrompida")
    print("=" * 60)
    
    # Mudar para o diretório do projeto
    os.chdir("e:/Desktop/StarLyraFireRed/Dynamic-Pokemon-Expansion-Gen-9-master")
    
    # Verificar integridade da ROM
    check_rom_integrity()
    
    # Fazer backup da ROM corrompida
    backup_current_rom()
    
    # Verificar corrupção específica
    is_clean = verify_rom_corruption()
    
    if not is_clean:
        # Sugerir fontes para ROM limpa
        suggest_rom_sources()
        
        print(f"\n🎯 CONCLUSÃO:")
        print(f"   A ROM base está corrompida com dados incorretos de Koga")
        print(f"   O sistema de randomização está funcionando corretamente")
        print(f"   O problema é a ROM base que já contém Species #92 (Gastly)")
        print(f"   em vez de #110 (Weezing) e #109 (Koffing)")
        
        return False
    else:
        print(f"\n✅ ROM LIMPA CONFIRMADA!")
        print(f"   A ROM base está correta")
        print(f"   O problema pode estar em outro lugar")
        
        return True

if __name__ == "__main__":
    main()

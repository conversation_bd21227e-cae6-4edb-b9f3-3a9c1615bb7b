#!/usr/bin/env python3
"""
Debug: Verificar como o sistema está lendo dados da ROM original
PROBLEMA: Sistema lendo dados incorretos da ROM para Koga
"""

import sys
import os
import struct

# Adicionar o diretório scripts ao path
sys.path.append('scripts')

def debug_rom_reading():
    """Debug da leitura de dados da ROM original"""
    
    print("🔍 DEBUG: Leitura de Dados da ROM Original")
    print("=" * 50)
    
    try:
        from insert import FindTrainerTable
        
        # Verificar Koga na ROM original
        koga_id = 418
        
        print(f"🎯 VERIFICAÇÃO DE KOGA (ID {koga_id}) NA ROM ORIGINAL:")
        
        with open("BPRE0.gba", "rb") as rom:
            trainer_table_offset = FindTrainerTable(rom)
            print(f"   Trainer table offset: 0x{trainer_table_offset:08X}")
            
            # Ler dados do trainer Koga
            trainer_offset = trainer_table_offset + (koga_id * 40)
            rom.seek(trainer_offset)
            trainer_data = rom.read(40)
            
            # Extrair informações do trainer
            trainer_class = trainer_data[1]
            party_size = trainer_data[32]
            party_ptr = struct.unpack('<I', trainer_data[36:40])[0]
            party_offset = party_ptr - 0x08000000
            
            print(f"   Trainer class: {trainer_class}")
            print(f"   Party size: {party_size}")
            print(f"   Party pointer: 0x{party_ptr:08X}")
            print(f"   Party offset: 0x{party_offset:08X}")
            
            # Ler party original
            rom.seek(party_offset)
            party_data = rom.read(party_size * 8)
            
            print(f"\n📋 PARTY ORIGINAL DE KOGA (ROM BPRE0.gba):")
            
            for i in range(party_size):
                pokemon_offset = i * 8
                pokemon_data = party_data[pokemon_offset:pokemon_offset + 8]
                
                if len(pokemon_data) < 8:
                    continue
                
                level = pokemon_data[2]
                species_id = struct.unpack('<H', pokemon_data[4:6])[0]
                
                # Mapear species para nome
                species_names = {
                    109: "Koffing", 89: "Muk", 110: "Weezing", 92: "Gastly",
                    77: "Ponyta", 58: "Growlithe", 3: "Venusaur"
                }
                
                species_name = species_names.get(species_id, f"Species_{species_id}")
                
                print(f"   Slot {i+1}: #{species_id:3d} {species_name:12s} Level {level}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro durante debug: {e}")
        import traceback
        traceback.print_exc()
        return False

def compare_rom_vs_system():
    """Compara leitura direta da ROM vs sistema"""
    
    print(f"\n🔍 COMPARAÇÃO: ROM Direta vs Sistema")
    print("=" * 45)
    
    try:
        from insert import ReadAllTrainerDataFromOriginalROM
        
        # Ler dados usando o sistema
        print(f"📋 LEITURA PELO SISTEMA:")
        original_trainer_cache = ReadAllTrainerDataFromOriginalROM()
        
        if original_trainer_cache and 'trainers' in original_trainer_cache:
            cached_trainers = original_trainer_cache['trainers']
            
            koga_id = 418
            if koga_id in cached_trainers:
                koga_info = cached_trainers[koga_id]
                original_party = koga_info['original_party']
                
                print(f"   Koga party (sistema): {len(original_party)} Pokémon")
                
                for i, pokemon in enumerate(original_party):
                    species = pokemon.get('species', 'Unknown')
                    level = pokemon.get('level', 'Unknown')
                    
                    # Mapear species para nome
                    species_names = {
                        109: "Koffing", 89: "Muk", 110: "Weezing", 92: "Gastly",
                        77: "Ponyta", 58: "Growlithe", 3: "Venusaur"
                    }
                    
                    species_name = species_names.get(species, f"Species_{species}")
                    
                    print(f"      Slot {i+1}: #{species:3d} {species_name:12s} Level {level}")
            else:
                print(f"   ❌ Koga não encontrado no cache")
        else:
            print(f"   ❌ Falha ao ler dados do sistema")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro durante comparação: {e}")
        import traceback
        traceback.print_exc()
        return False

def analyze_party_reading_function():
    """Analisa a função de leitura de party"""
    
    print(f"\n🔍 ANÁLISE: Função de Leitura de Party")
    print("=" * 45)
    
    try:
        # Verificar se há problemas na função ReadOriginalTrainerPartyData
        print(f"📋 POSSÍVEIS PROBLEMAS:")
        print(f"   1. Offset de party incorreto")
        print(f"   2. Tamanho de entry incorreto (deveria ser 8 bytes)")
        print(f"   3. Leitura de species no offset errado (deveria ser +4)")
        print(f"   4. Endianness incorreto (deveria ser little-endian)")
        print(f"   5. Confusão entre trainer ID e offset")
        
        # Verificar função específica
        with open("scripts/insert.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # Procurar pela função ReadOriginalTrainerPartyData
        if "ReadOriginalTrainerPartyData" in content:
            print(f"\n✅ Função ReadOriginalTrainerPartyData encontrada")
            
            # Extrair a função
            start = content.find("def ReadOriginalTrainerPartyData")
            if start != -1:
                end = content.find("\ndef ", start + 1)
                if end == -1:
                    end = content.find("\nclass ", start + 1)
                if end == -1:
                    end = len(content)
                
                function_code = content[start:end]
                
                # Verificar problemas comuns
                issues = []
                
                if "pokemon_offset + 4" not in function_code:
                    issues.append("❌ Species offset pode estar incorreto")
                
                if "struct.unpack('<H'" not in function_code:
                    issues.append("❌ Endianness pode estar incorreto")
                
                if "* 8" not in function_code:
                    issues.append("❌ Entry size pode estar incorreto")
                
                if issues:
                    print(f"\n🚨 PROBLEMAS ENCONTRADOS:")
                    for issue in issues:
                        print(f"   {issue}")
                else:
                    print(f"\n✅ Função parece estar correta")
        else:
            print(f"\n❌ Função ReadOriginalTrainerPartyData não encontrada")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro durante análise: {e}")
        import traceback
        traceback.print_exc()
        return False

def verify_trainer_table_reading():
    """Verifica se a tabela de trainers está sendo lida corretamente"""
    
    print(f"\n🔍 VERIFICAÇÃO: Leitura da Tabela de Trainers")
    print("=" * 50)
    
    try:
        from insert import FindTrainerTable
        
        with open("BPRE0.gba", "rb") as rom:
            trainer_table_offset = FindTrainerTable(rom)
            
            print(f"📋 TABELA DE TRAINERS:")
            print(f"   Offset: 0x{trainer_table_offset:08X}")
            
            # Verificar alguns trainers conhecidos
            known_trainers = [
                (414, "BROCK"),
                (415, "MISTY"), 
                (416, "LT_SURGE"),
                (417, "ERIKA"),
                (418, "KOGA"),
                (419, "SABRINA"),
                (420, "BLAINE"),
                (350, "GIOVANNI")
            ]
            
            for trainer_id, trainer_name in known_trainers:
                trainer_offset = trainer_table_offset + (trainer_id * 40)
                rom.seek(trainer_offset)
                trainer_data = rom.read(40)
                
                trainer_class = trainer_data[1]
                party_size = trainer_data[32]
                party_ptr = struct.unpack('<I', trainer_data[36:40])[0]
                
                print(f"   {trainer_name:10s} (ID {trainer_id:3d}): Class {trainer_class:2d}, Size {party_size}, Ptr 0x{party_ptr:08X}")
                
                # Verificar se o pointer é válido
                if party_ptr < 0x08000000 or party_ptr > 0x09FFFFFF:
                    print(f"      🚨 POINTER INVÁLIDO!")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro durante verificação: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    # Mudar para o diretório do projeto
    os.chdir("e:/Desktop/StarLyraFireRed/Dynamic-Pokemon-Expansion-Gen-9-master")
    
    # Executar debug
    debug_rom_reading()
    compare_rom_vs_system()
    analyze_party_reading_function()
    verify_trainer_table_reading()
